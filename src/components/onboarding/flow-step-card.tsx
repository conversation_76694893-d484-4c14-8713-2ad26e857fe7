'use client';

import { Bad<PERSON>, Card, CardContent, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { 
	CheckCircle2, 
	Lock, 
	AlertCircle,
	ArrowRight,
	Sparkles
} from 'lucide-react';
import { LucideIcon } from 'lucide-react';

interface FlowStepCardProps {
	stepNumber: number;
	titleKey: string;
	messageKey: string;
	icon: LucideIcon;
	status: 'completed' | 'available' | 'locked' | 'current';
	statusMessage?: string;
	statusValues?: Record<string, any>;
	children: React.ReactNode;
	className?: string;
}

export function FlowStepCard({
	stepNumber,
	titleKey,
	messageKey,
	icon: Icon,
	status,
	statusMessage,
	statusValues,
	children,
	className = ''
}: FlowStepCardProps) {
	const getStatusConfig = () => {
		switch (status) {
			case 'completed':
				return {
					borderColor: 'border-green-200 dark:border-green-800',
					bgColor: 'bg-green-50 dark:bg-green-900/20',
					stepBg: 'bg-green-600',
					stepIcon: CheckCircle2,
					stepIconColor: 'text-white',
					titleColor: 'text-green-800 dark:text-green-200',
					messageColor: 'text-green-700 dark:text-green-300',
					badge: { text: 'Completed', color: 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700' }
				};
			case 'current':
				return {
					borderColor: 'border-orange-200 dark:border-orange-800',
					bgColor: 'bg-orange-50 dark:bg-orange-900/20',
					stepBg: 'bg-orange-600',
					stepIcon: Sparkles,
					stepIconColor: 'text-white',
					titleColor: 'text-orange-800 dark:text-orange-200',
					messageColor: 'text-orange-700 dark:text-orange-300',
					badge: { text: 'Current Step', color: 'bg-orange-100 text-orange-700 border-orange-300 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700' }
				};
			case 'available':
				return {
					borderColor: 'border-blue-200 dark:border-blue-800',
					bgColor: 'bg-blue-50 dark:bg-blue-900/20',
					stepBg: 'bg-blue-600',
					stepIcon: ArrowRight,
					stepIconColor: 'text-white',
					titleColor: 'text-blue-800 dark:text-blue-200',
					messageColor: 'text-blue-700 dark:text-blue-300',
					badge: { text: 'Available', color: 'bg-blue-100 text-blue-700 border-blue-300 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700' }
				};
			case 'locked':
				return {
					borderColor: 'border-muted',
					bgColor: 'bg-muted/30',
					stepBg: 'bg-muted-foreground/50',
					stepIcon: Lock,
					stepIconColor: 'text-muted-foreground',
					titleColor: 'text-muted-foreground',
					messageColor: 'text-muted-foreground/70',
					badge: { text: 'Locked', color: 'bg-muted text-muted-foreground' }
				};
			default:
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-muted',
					stepIcon: Icon,
					stepIconColor: 'text-muted-foreground',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					badge: null
				};
		}
	};

	const config = getStatusConfig();
	const StepIcon = config.stepIcon;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5, delay: stepNumber * 0.1 }}
			className={className}
		>
			<Card className={`
				relative overflow-hidden transition-all duration-300
				${config.borderColor} ${config.bgColor}
				${status === 'current' ? 'shadow-lg shadow-orange-200/50 dark:shadow-orange-900/20' : ''}
				${status === 'available' ? 'hover:shadow-md' : ''}
			`}>
				{/* Step indicator */}
				<div className="absolute top-4 left-4 z-10">
					<div className={`
						w-10 h-10 rounded-full flex items-center justify-center
						${config.stepBg}
					`}>
						{status === 'completed' || status === 'current' || status === 'available' ? (
							<StepIcon className={`h-5 w-5 ${config.stepIconColor}`} />
						) : (
							<span className="text-sm font-bold text-white">{stepNumber}</span>
						)}
					</div>
				</div>

				{/* Status badge */}
				{config.badge && (
					<div className="absolute top-4 right-4 z-10">
						<Badge className={config.badge.color}>
							{config.badge.text}
						</Badge>
					</div>
				)}

				<CardContent className="pt-16 pb-6 px-6">
					{/* Header */}
					<div className="mb-4">
						<h3 className={`text-xl font-bold mb-2 ${config.titleColor}`}>
							<Translate text={titleKey} />
						</h3>
						<p className={`text-sm leading-relaxed ${config.messageColor}`}>
							<Translate text={messageKey} />
						</p>
					</div>

					{/* Status message */}
					{statusMessage && (
						<div className="mb-4">
							<div className={`
								flex items-start gap-2 p-3 rounded-lg
								${status === 'locked' 
									? 'bg-muted/50 border border-muted' 
									: status === 'current'
										? 'bg-orange-100/50 border border-orange-200 dark:bg-orange-900/10 dark:border-orange-800/50'
										: 'bg-background/50 border border-border/50'
								}
							`}>
								{status === 'locked' && <AlertCircle className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />}
								{status === 'current' && <Sparkles className="h-4 w-4 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />}
								{status === 'available' && <CheckCircle2 className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />}
								<p className={`text-sm ${config.messageColor}`}>
									<Translate text={statusMessage} values={statusValues} />
								</p>
							</div>
						</div>
					)}

					{/* Feature content */}
					<div className={`
						${status === 'locked' ? 'opacity-60 pointer-events-none' : ''}
					`}>
						{children}
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
