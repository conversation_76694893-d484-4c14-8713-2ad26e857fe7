'use client';

import { Bad<PERSON>, Card, CardContent, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { Lock, AlertCircle } from 'lucide-react';
import { LucideIcon } from 'lucide-react';

interface FlowStepCardProps {
	stepNumber: number;
	titleKey: string;
	messageKey: string;
	icon: LucideIcon;
	status: 'available' | 'locked';
	statusMessage?: string;
	statusValues?: Record<string, any>;
	children: React.ReactNode;
	className?: string;
}

export function FlowStepCard({
	stepNumber,
	titleKey,
	messageKey,
	icon: Icon,
	status,
	statusMessage,
	statusValues,
	children,
	className = '',
}: FlowStepCardProps) {
	const getStatusConfig = () => {
		switch (status) {
			case 'available':
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-slate-600 dark:bg-slate-500',
					stepIcon: Icon,
					stepIconColor: 'text-white',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					badge: null,
				};
			case 'locked':
				return {
					borderColor: 'border-muted',
					bgColor: 'bg-muted/30',
					stepBg: 'bg-muted-foreground/50',
					stepIcon: Lock,
					stepIconColor: 'text-muted-foreground',
					titleColor: 'text-muted-foreground',
					messageColor: 'text-muted-foreground/70',
					badge: { text: 'Locked', color: 'bg-muted text-muted-foreground' },
				};
			default:
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-slate-600 dark:bg-slate-500',
					stepIcon: Icon,
					stepIconColor: 'text-white',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					badge: null,
				};
		}
	};

	const config = getStatusConfig();
	const StepIcon = config.stepIcon;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5, delay: stepNumber * 0.1 }}
			className={className}
		>
			<Card
				className={`
				relative overflow-hidden transition-all duration-300
				${config.borderColor} ${config.bgColor}
				${status === 'available' ? 'hover:shadow-md' : ''}
			`}
			>
				{/* Step indicator */}
				<div className="absolute top-4 left-4 z-10">
					<div
						className={`
						w-10 h-10 rounded-full flex items-center justify-center
						${config.stepBg}
					`}
					>
						{status === 'completed' ||
						status === 'current' ||
						status === 'available' ? (
							<StepIcon className={`h-5 w-5 ${config.stepIconColor}`} />
						) : (
							<span className="text-sm font-bold text-white">{stepNumber}</span>
						)}
					</div>
				</div>

				{/* Status badge */}
				{config.badge && (
					<div className="absolute top-4 right-4 z-10">
						<Badge className={config.badge.color}>{config.badge.text}</Badge>
					</div>
				)}

				<CardContent className="pt-16 pb-6 px-6">
					{/* Header */}
					<div className="mb-4">
						<h3 className={`text-xl font-bold mb-2 ${config.titleColor}`}>
							<Translate text={titleKey} />
						</h3>
						<p className={`text-sm leading-relaxed ${config.messageColor}`}>
							<Translate text={messageKey} />
						</p>
					</div>

					{/* Status message */}
					{statusMessage && (
						<div className="mb-4">
							<div className="flex items-start gap-2 p-3 rounded-lg bg-muted/50 border border-muted">
								{status === 'locked' && (
									<AlertCircle className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
								)}
								<p className={`text-sm ${config.messageColor}`}>
									<Translate text={statusMessage} values={statusValues} />
								</p>
							</div>
						</div>
					)}

					{/* Feature content */}
					<div
						className={`
						${status === 'locked' ? 'opacity-60 pointer-events-none' : ''}
					`}
					>
						{children}
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);
}
