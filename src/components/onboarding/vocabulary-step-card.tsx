'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { Lock, ArrowRight } from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import Link from 'next/link';

interface VocabularyStepCardProps {
	stepNumber: string;
	titleKey: string;
	messageKey: string;
	icon: LucideIcon;
	status: 'available' | 'locked';
	statusMessage?: string;
	statusValues?: Record<string, any>;
	href: string;
	className?: string;
}

export function VocabularyStepCard({
	stepNumber,
	titleKey,
	messageKey,
	icon: Icon,
	status,
	statusMessage,
	statusValues,
	href,
	className = '',
}: VocabularyStepCardProps) {
	const getStatusConfig = () => {
		switch (status) {
			case 'available':
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-slate-600 dark:bg-slate-500',
					stepIcon: Icon,
					stepIconColor: 'text-white',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					buttonVariant: 'outline' as const,
					buttonText: 'Go to Section',
					disabled: false,
				};
			case 'locked':
				return {
					borderColor: 'border-muted',
					bgColor: 'bg-muted/30',
					stepBg: 'bg-muted-foreground/50',
					stepIcon: Lock,
					stepIconColor: 'text-muted-foreground',
					titleColor: 'text-muted-foreground',
					messageColor: 'text-muted-foreground/70',
					buttonVariant: 'outline' as const,
					buttonText: 'Locked',
					disabled: true,
				};
			default:
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-slate-600 dark:bg-slate-500',
					stepIcon: Icon,
					stepIconColor: 'text-white',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					buttonVariant: 'outline' as const,
					buttonText: 'Go to Section',
					disabled: false,
				};
		}
	};

	const config = getStatusConfig();
	const StepIcon = config.stepIcon;

	const content = (
		<motion.div
			initial={{ opacity: 0, x: -20 }}
			animate={{ opacity: 1, x: 0 }}
			transition={{ duration: 0.3, delay: parseFloat(stepNumber) * 0.1 }}
			className={className}
		>
			<Card
				className={`
				relative overflow-hidden transition-all duration-300 group
				${config.borderColor} ${config.bgColor}
				${!config.disabled ? 'hover:shadow-md cursor-pointer' : 'opacity-75'}
			`}
			>
				<CardContent className="p-4">
					<div className="flex items-center gap-4">
						{/* Step indicator */}
						<div
							className={`
							w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0
							${config.stepBg}
						`}
						>
							<StepIcon className={`h-5 w-5 ${config.stepIconColor}`} />
						</div>

						{/* Content */}
						<div className="flex-1 min-w-0">
							<h4 className={`font-semibold mb-1 ${config.titleColor}`}>
								<Translate text={titleKey} />
							</h4>
							<p className={`text-sm leading-relaxed mb-2 ${config.messageColor}`}>
								<Translate text={messageKey} />
							</p>

							{/* Status message */}
							{statusMessage && (
								<p className={`text-xs ${config.messageColor} opacity-80`}>
									<Translate text={statusMessage} values={statusValues} />
								</p>
							)}
						</div>

						{/* Action button */}
						<div className="flex-shrink-0">
							<Button
								variant={config.buttonVariant}
								size="sm"
								disabled={config.disabled}
								className={`
									transition-all duration-200
									${!config.disabled ? 'group-hover:translate-x-1' : ''}
								`}
							>
								{!config.disabled && <ArrowRight className="h-4 w-4 mr-1" />}
								{status === 'locked' && <Lock className="h-4 w-4 mr-1" />}
								{config.buttonText}
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);

	if (config.disabled) {
		return content;
	}

	return (
		<Link href={href} className="block">
			{content}
		</Link>
	);
}
