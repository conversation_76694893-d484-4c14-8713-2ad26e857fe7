'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { 
	CheckCircle2, 
	Lock, 
	ArrowRight,
	Sparkles,
	Circle
} from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import Link from 'next/link';

interface VocabularyStepCardProps {
	stepNumber: string;
	titleKey: string;
	messageKey: string;
	icon: LucideIcon;
	status: 'completed' | 'available' | 'locked' | 'current';
	statusMessage?: string;
	statusValues?: Record<string, any>;
	href: string;
	className?: string;
}

export function VocabularyStepCard({
	stepNumber,
	titleKey,
	messageKey,
	icon: Icon,
	status,
	statusMessage,
	statusValues,
	href,
	className = ''
}: VocabularyStepCardProps) {
	const getStatusConfig = () => {
		switch (status) {
			case 'completed':
				return {
					borderColor: 'border-green-200 dark:border-green-800',
					bgColor: 'bg-green-50 dark:bg-green-900/20',
					stepBg: 'bg-green-600',
					stepIcon: CheckCircle2,
					stepIconColor: 'text-white',
					titleColor: 'text-green-800 dark:text-green-200',
					messageColor: 'text-green-700 dark:text-green-300',
					buttonVariant: 'outline' as const,
					buttonText: 'Completed',
					disabled: false
				};
			case 'current':
				return {
					borderColor: 'border-orange-200 dark:border-orange-800',
					bgColor: 'bg-orange-50 dark:bg-orange-900/20',
					stepBg: 'bg-orange-600',
					stepIcon: Sparkles,
					stepIconColor: 'text-white',
					titleColor: 'text-orange-800 dark:text-orange-200',
					messageColor: 'text-orange-700 dark:text-orange-300',
					buttonVariant: 'default' as const,
					buttonText: 'Start Now',
					disabled: false
				};
			case 'available':
				return {
					borderColor: 'border-blue-200 dark:border-blue-800',
					bgColor: 'bg-blue-50 dark:bg-blue-900/20',
					stepBg: 'bg-blue-600',
					stepIcon: Circle,
					stepIconColor: 'text-white',
					titleColor: 'text-blue-800 dark:text-blue-200',
					messageColor: 'text-blue-700 dark:text-blue-300',
					buttonVariant: 'outline' as const,
					buttonText: 'Go to Section',
					disabled: false
				};
			case 'locked':
				return {
					borderColor: 'border-muted',
					bgColor: 'bg-muted/30',
					stepBg: 'bg-muted-foreground/50',
					stepIcon: Lock,
					stepIconColor: 'text-muted-foreground',
					titleColor: 'text-muted-foreground',
					messageColor: 'text-muted-foreground/70',
					buttonVariant: 'outline' as const,
					buttonText: 'Locked',
					disabled: true
				};
			default:
				return {
					borderColor: 'border-border',
					bgColor: 'bg-background',
					stepBg: 'bg-muted',
					stepIcon: Icon,
					stepIconColor: 'text-muted-foreground',
					titleColor: 'text-foreground',
					messageColor: 'text-muted-foreground',
					buttonVariant: 'outline' as const,
					buttonText: 'Go to Section',
					disabled: false
				};
		}
	};

	const config = getStatusConfig();
	const StepIcon = config.stepIcon;

	const content = (
		<motion.div
			initial={{ opacity: 0, x: -20 }}
			animate={{ opacity: 1, x: 0 }}
			transition={{ duration: 0.3, delay: parseFloat(stepNumber) * 0.1 }}
			className={className}
		>
			<Card className={`
				relative overflow-hidden transition-all duration-300 group
				${config.borderColor} ${config.bgColor}
				${status === 'current' ? 'shadow-md shadow-orange-200/50 dark:shadow-orange-900/20' : ''}
				${!config.disabled ? 'hover:shadow-md cursor-pointer' : 'opacity-75'}
			`}>
				<CardContent className="p-4">
					<div className="flex items-center gap-4">
						{/* Step indicator */}
						<div className={`
							w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0
							${config.stepBg}
						`}>
							<StepIcon className={`h-5 w-5 ${config.stepIconColor}`} />
						</div>

						{/* Content */}
						<div className="flex-1 min-w-0">
							<h4 className={`font-semibold mb-1 ${config.titleColor}`}>
								<Translate text={titleKey} />
							</h4>
							<p className={`text-sm leading-relaxed mb-2 ${config.messageColor}`}>
								<Translate text={messageKey} />
							</p>
							
							{/* Status message */}
							{statusMessage && (
								<p className={`text-xs ${config.messageColor} opacity-80`}>
									<Translate text={statusMessage} values={statusValues} />
								</p>
							)}
						</div>

						{/* Action button */}
						<div className="flex-shrink-0">
							<Button
								variant={config.buttonVariant}
								size="sm"
								disabled={config.disabled}
								className={`
									transition-all duration-200
									${status === 'current' 
										? 'bg-orange-600 hover:bg-orange-700 text-white shadow-lg shadow-orange-600/25' 
										: status === 'completed'
											? 'border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300'
											: status === 'available'
												? 'border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-700 dark:text-blue-300'
												: ''
									}
									${!config.disabled ? 'group-hover:translate-x-1' : ''}
								`}
							>
								{!config.disabled && status !== 'completed' && (
									<ArrowRight className="h-4 w-4 mr-1" />
								)}
								{status === 'completed' && (
									<CheckCircle2 className="h-4 w-4 mr-1" />
								)}
								{status === 'locked' && (
									<Lock className="h-4 w-4 mr-1" />
								)}
								{config.buttonText}
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</motion.div>
	);

	if (config.disabled) {
		return content;
	}

	return (
		<Link href={href} className="block">
			{content}
		</Link>
	);
}
