'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, Translate } from '@/components/ui';
import { ErrorDisplay } from '@/components/ui/error-display';
import { FeatureCard } from '@/components/ui/feature-card';
import { InteractiveTutorial, FlowStepCard } from '@/components/onboarding';
import { useCollections } from '@/hooks';
import { useTranslation } from '@/contexts';
import {
	CheckCircle2,
	Edit3,
	FileText,
	GraduationCap,
	ListChecks,
	MessageSquare,
	RefreshCw,
	Target,
	TrendingUp,
	Zap,
} from 'lucide-react';
import { useState } from 'react';
import { CollectionStatsCard } from './collection-stats-card';

function CollectionOverviewSkeleton() {
	return (
		<div className="container mx-auto py-8 space-y-8">
			{/* Collection Info Card Skeleton */}
			<div className="relative overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-muted/50 to-muted/30 rounded-2xl animate-pulse" />
				<Card className="relative border-muted/50 bg-background/80 backdrop-blur-sm">
					<CardHeader className="pb-4">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-xl bg-muted/50 animate-pulse">
								<div className="h-8 w-8 bg-muted rounded" />
							</div>
							<div className="h-8 w-80 bg-muted rounded-lg animate-pulse" />
						</div>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
							{[...Array(5)].map((_, i) => (
								<div
									key={i}
									className="p-4 rounded-xl bg-muted/30 border border-muted/50 animate-pulse"
								>
									<div className="flex items-center gap-3">
										<div className="p-2 rounded-lg bg-muted/50">
											<div className="h-5 w-5 bg-muted rounded" />
										</div>
										<div className="flex-1 space-y-2">
											<div className="h-3 w-20 bg-muted rounded" />
											<div className="h-4 w-24 bg-muted rounded" />
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Feature Cards Skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
				{[...Array(3)].map((_, i) => (
					<Card
						key={i}
						className="h-80 border-muted/50 bg-gradient-to-br from-background to-muted/20 animate-pulse"
					>
						<CardHeader className="pb-4">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="p-2 rounded-xl bg-muted/50">
										<div className="h-6 w-6 bg-muted rounded" />
									</div>
									<div className="h-6 w-32 bg-muted rounded" />
								</div>
								<div className="h-4 w-4 bg-muted rounded" />
							</div>
							<div className="h-4 w-48 bg-muted rounded mt-3" />
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{[...Array(4)].map((_, j) => (
									<div
										key={j}
										className="flex items-center gap-3 p-3 rounded-lg bg-muted/30"
									>
										<div className="h-4 w-4 bg-muted rounded" />
										<div className="h-4 w-32 bg-muted rounded" />
										<div className="ml-auto h-4 w-4 bg-muted rounded" />
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}

export function CollectionOverviewClient() {
	const { currentCollection, loading, error, setError } = useCollections();
	const { t } = useTranslation();

	// Tutorial state
	const [showTutorial, setShowTutorial] = useState(false);

	const wordCount = currentCollection?.word_ids.length || 0;
	const hasWords = wordCount > 0;
	const hasEnoughWordsForQuiz = wordCount >= 10;

	const handleTutorialComplete = () => {
		setShowTutorial(false);
		if (currentCollection) {
			localStorage.setItem(`tutorial-completed-${currentCollection.id}`, 'true');
		}
	};

	const getStepStatus = (
		stepNumber: number
	): 'completed' | 'available' | 'locked' | 'current' => {
		switch (stepNumber) {
			case 1: // Vocabulary step
				if (wordCount === 0) return 'current';
				return wordCount > 0 ? 'completed' : 'current';
			case 2: // Paragraph step
				if (wordCount === 0) return 'locked';
				if (wordCount < 10) return 'current';
				return 'available';
			case 3: // Stats step
				return wordCount > 0 ? 'available' : 'locked';
			default:
				return 'locked';
		}
	};

	const getStepMessage = (stepNumber: number) => {
		switch (stepNumber) {
			case 1:
				if (wordCount === 0) return { key: 'collections.flow.step1_empty', values: {} };
				return { key: 'collections.flow.step1_progress', values: { count: wordCount } };
			case 2:
				if (wordCount < 10)
					return {
						key: 'collections.flow.step2_locked',
						values: { count: 10 - wordCount },
					};
				return { key: 'collections.flow.step2_available', values: {} };
			case 3:
				return { key: 'collections.flow.step3_available', values: {} };
			default:
				return null;
		}
	};

	const flowSteps = currentCollection
		? [
				{
					stepNumber: 1,
					titleKey: 'collections.flow.step1_title',
					messageKey: 'collections.flow.step1_message',
					icon: GraduationCap,
					status: getStepStatus(1),
					statusMessage: getStepMessage(1),
					feature: {
						titleKey: 'collections.tabs.vocabulary',
						descriptionKey: 'collections.overview.vocabulary_desc',
						icon: GraduationCap,
						link: `/collections/${currentCollection.id}/vocabulary`,
						tutorialTarget: 'vocabulary-section',
						subFeatures: [
							{
								titleKey: 'collections.tabs.generate_words',
								icon: Zap,
								subLink: '/generate',
							},
							{
								titleKey: 'collections.tabs.my_words_list',
								icon: ListChecks,
								subLink: '/my-words',
							},
							{
								titleKey: 'collections.tabs.review',
								icon: RefreshCw,
								subLink: '/review',
								disabled: !hasWords,
								disabledReason: t('collections.overview.review_disabled_reason'),
							},
							{
								titleKey: 'collections.tabs.multiple_choice_practice',
								icon: Target,
								subLink: '/mcq',
								disabled: !hasEnoughWordsForQuiz,
								disabledReason: t('collections.overview.quiz_disabled_reason'),
							},
						],
					},
				},
				{
					stepNumber: 2,
					titleKey: 'collections.flow.step2_title',
					messageKey: 'collections.flow.step2_message',
					icon: FileText,
					status: getStepStatus(2),
					statusMessage: getStepMessage(2),
					feature: {
						titleKey: 'collections.tabs.paragraphs',
						descriptionKey: 'collections.overview.paragraphs_desc',
						icon: FileText,
						link: `/collections/${currentCollection.id}/paragraph`,
						tutorialTarget: 'paragraph-section',
						subFeatures: [
							{
								titleKey: 'collections.tabs.paragraph_practice',
								icon: Edit3,
								subLink: '/paragraph-practice',
							},
							{
								titleKey: 'qa_practice.tab_title',
								icon: MessageSquare,
								subLink: '/qa-practice',
							},
							{
								titleKey: 'collections.tabs.grammar_practice',
								icon: CheckCircle2,
								subLink: '/grammar-practice',
							},
						],
					},
				},
				{
					stepNumber: 3,
					titleKey: 'collections.flow.step3_title',
					messageKey: 'collections.flow.step3_message',
					icon: TrendingUp,
					status: getStepStatus(3),
					statusMessage: getStepMessage(3),
					feature: {
						titleKey: 'collections.stats.title',
						descriptionKey: 'collections.stats.description',
						icon: TrendingUp,
						link: `/collections/${currentCollection.id}/stats`,
						tutorialTarget: 'stats-section',
						subFeatures: [],
					},
				},
		  ]
		: [];

	// Show loading skeleton while collection is being set or loaded
	if (loading.setCurrent || loading.get || !currentCollection) {
		return <CollectionOverviewSkeleton />;
	}

	return (
		<>
			<ErrorDisplay error={error} onDismiss={() => setError(null)} />

			{/* Interactive Tutorial */}
			<InteractiveTutorial
				isOpen={showTutorial}
				onClose={() => setShowTutorial(false)}
				onComplete={handleTutorialComplete}
			/>

			<div className="min-h-screen">
				<div className="container mx-auto pb-8 space-y-8">
					<CollectionStatsCard collection={currentCollection} />

					{/* Learning Flow Section */}
					<div className="space-y-6">
						<div className="flex items-center gap-3">
							<h2 className="text-2xl font-bold">
								<Translate text="collections.overview.features_title" />
							</h2>
							<div className="h-px flex-1 bg-gradient-to-r from-border to-transparent" />
						</div>

						<div className="space-y-6">
							{flowSteps.map((step) => (
								<FlowStepCard
									key={step.stepNumber}
									stepNumber={step.stepNumber}
									titleKey={step.titleKey}
									messageKey={step.messageKey}
									icon={step.icon}
									status={step.status}
									statusMessage={step.statusMessage?.key}
									statusValues={step.statusMessage?.values}
								>
									<FeatureCard
										titleKey={step.feature.titleKey}
										descriptionKey={step.feature.descriptionKey}
										icon={step.feature.icon}
										link={step.feature.link}
										subFeatures={step.feature.subFeatures}
										tutorialTarget={step.feature.tutorialTarget}
									/>
								</FlowStepCard>
							))}
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
