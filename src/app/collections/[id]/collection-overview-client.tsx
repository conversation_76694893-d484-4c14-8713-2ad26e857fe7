'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Translate } from '@/components/ui';
import { ErrorDisplay } from '@/components/ui/error-display';
import { FeatureCard } from '@/components/ui/feature-card';
import {
	WelcomeBanner,
	QuickStartGuide,
	ProgressIndicator,
	InteractiveTutorial,
} from '@/components/onboarding';
import { useCollections } from '@/hooks';
import { useTranslation } from '@/contexts';
import { cn } from '@/lib';
import {
	CheckCircle2,
	Edit3,
	FileText,
	GraduationCap,
	ListChecks,
	MessageSquare,
	RefreshCw,
	Target,
	TrendingUp,
	Zap,
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { CollectionStatsCard } from './collection-stats-card';

function CollectionOverviewSkeleton() {
	return (
		<div className="container mx-auto py-8 space-y-8">
			{/* Collection Info Card Skeleton */}
			<div className="relative overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-muted/50 to-muted/30 rounded-2xl animate-pulse" />
				<Card className="relative border-muted/50 bg-background/80 backdrop-blur-sm">
					<CardHeader className="pb-4">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-xl bg-muted/50 animate-pulse">
								<div className="h-8 w-8 bg-muted rounded" />
							</div>
							<div className="h-8 w-80 bg-muted rounded-lg animate-pulse" />
						</div>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
							{[...Array(5)].map((_, i) => (
								<div
									key={i}
									className="p-4 rounded-xl bg-muted/30 border border-muted/50 animate-pulse"
								>
									<div className="flex items-center gap-3">
										<div className="p-2 rounded-lg bg-muted/50">
											<div className="h-5 w-5 bg-muted rounded" />
										</div>
										<div className="flex-1 space-y-2">
											<div className="h-3 w-20 bg-muted rounded" />
											<div className="h-4 w-24 bg-muted rounded" />
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Feature Cards Skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
				{[...Array(3)].map((_, i) => (
					<Card
						key={i}
						className="h-80 border-muted/50 bg-gradient-to-br from-background to-muted/20 animate-pulse"
					>
						<CardHeader className="pb-4">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="p-2 rounded-xl bg-muted/50">
										<div className="h-6 w-6 bg-muted rounded" />
									</div>
									<div className="h-6 w-32 bg-muted rounded" />
								</div>
								<div className="h-4 w-4 bg-muted rounded" />
							</div>
							<div className="h-4 w-48 bg-muted rounded mt-3" />
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{[...Array(4)].map((_, j) => (
									<div
										key={j}
										className="flex items-center gap-3 p-3 rounded-lg bg-muted/30"
									>
										<div className="h-4 w-4 bg-muted rounded" />
										<div className="h-4 w-32 bg-muted rounded" />
										<div className="ml-auto h-4 w-4 bg-muted rounded" />
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}

export function CollectionOverviewClient() {
	const { currentCollection, loading, error, setError } = useCollections();
	const { t } = useTranslation();

	// Onboarding state
	const [showWelcomeBanner, setShowWelcomeBanner] = useState(true);
	const [showTutorial, setShowTutorial] = useState(false);
	const [isFirstTime, setIsFirstTime] = useState(false);

	const wordCount = currentCollection?.word_ids.length || 0;
	const hasWords = wordCount > 0;
	const hasEnoughWordsForQuiz = wordCount >= 10;

	// Mock data for progress tracking (in real app, this would come from API)
	const reviewCount = 0; // TODO: Get from stats API
	const quizCount = 0; // TODO: Get from stats API

	// Check if this is user's first time with this collection
	useEffect(() => {
		if (currentCollection) {
			const hasSeenWelcome = localStorage.getItem(`welcome-seen-${currentCollection.id}`);
			const isNewCollection = wordCount === 0 && !hasSeenWelcome;
			setIsFirstTime(isNewCollection);
			setShowWelcomeBanner(!hasSeenWelcome || isNewCollection);
		}
	}, [currentCollection, wordCount]);

	const handleWelcomeDismiss = () => {
		if (currentCollection) {
			localStorage.setItem(`welcome-seen-${currentCollection.id}`, 'true');
		}
		setShowWelcomeBanner(false);
	};

	const handleStartTutorial = () => {
		setShowTutorial(true);
	};

	const handleTutorialComplete = () => {
		setShowTutorial(false);
		if (currentCollection) {
			localStorage.setItem(`tutorial-completed-${currentCollection.id}`, 'true');
		}
	};

	const features = currentCollection
		? [
				{
					titleKey: 'collections.tabs.vocabulary',
					descriptionKey: 'collections.overview.vocabulary_desc',
					icon: GraduationCap,
					link: `/collections/${currentCollection.id}/vocabulary`,
					className: '',
					status: wordCount === 0 ? 'recommended' : 'available',
					wordCount,
					tutorialTarget: 'vocabulary-section',
					subFeatures: [
						{
							titleKey: 'collections.tabs.generate_words',
							icon: Zap,
							subLink: '/generate',
						},
						{
							titleKey: 'collections.tabs.my_words_list',
							icon: ListChecks,
							subLink: '/my-words',
						},
						{
							titleKey: 'collections.tabs.review',
							icon: RefreshCw,
							subLink: '/review',
							disabled: !hasWords,
							disabledReason: t('collections.overview.review_disabled_reason'),
						},
						{
							titleKey: 'collections.tabs.multiple_choice_practice',
							icon: Target,
							subLink: '/mcq',
							disabled: !hasEnoughWordsForQuiz,
							disabledReason: t('collections.overview.quiz_disabled_reason'),
						},
					],
				},
				{
					titleKey: 'collections.tabs.paragraphs',
					descriptionKey: 'collections.overview.paragraphs_desc',
					icon: FileText,
					link: `/collections/${currentCollection.id}/paragraph`,
					className: '',
					status: wordCount >= 10 ? 'available' : 'locked',
					wordCount,
					requiredWords: 10,
					tutorialTarget: 'paragraph-section',
					subFeatures: [
						{
							titleKey: 'collections.tabs.paragraph_practice',
							icon: Edit3,
							subLink: '/paragraph-practice',
						},
						{
							titleKey: 'qa_practice.tab_title',
							icon: MessageSquare,
							subLink: '/qa-practice',
						},
						{
							titleKey: 'collections.tabs.grammar_practice',
							icon: CheckCircle2,
							subLink: '/grammar-practice',
						},
					],
				},
				{
					titleKey: 'collections.stats.title',
					descriptionKey: 'collections.stats.description',
					icon: TrendingUp,
					link: `/collections/${currentCollection.id}/stats`,
					className: 'col-span-1 md:col-span-2',
					status: 'available',
					tutorialTarget: 'stats-section',
					subFeatures: [],
				},
		  ]
		: [];

	// Show loading skeleton while collection is being set or loaded
	if (loading.setCurrent || loading.get || !currentCollection) {
		return <CollectionOverviewSkeleton />;
	}

	return (
		<>
			<ErrorDisplay error={error} onDismiss={() => setError(null)} />

			{/* Interactive Tutorial */}
			<InteractiveTutorial
				isOpen={showTutorial}
				onClose={() => setShowTutorial(false)}
				onComplete={handleTutorialComplete}
			/>

			<div className="min-h-screen">
				<div className="container mx-auto pb-8 space-y-8">
					{/* Welcome Banner for new users */}
					{showWelcomeBanner && (
						<WelcomeBanner
							collectionId={currentCollection.id}
							wordCount={wordCount}
							onDismiss={handleWelcomeDismiss}
							isFirstTime={isFirstTime}
						/>
					)}

					<CollectionStatsCard collection={currentCollection} />

					{/* Progress Indicator for new users */}
					{wordCount < 50 && (
						<ProgressIndicator
							wordCount={wordCount}
							reviewCount={reviewCount}
							quizCount={quizCount}
						/>
					)}

					{/* Quick Start Guide for users with few words */}
					{wordCount < 20 && (
						<QuickStartGuide
							collectionId={currentCollection.id}
							wordCount={wordCount}
							hasReviewed={reviewCount > 0}
							hasCompletedQuiz={quizCount > 0}
						/>
					)}

					{/* Features Section */}
					<div className="space-y-6">
						<div className="flex items-center gap-3">
							<h2 className="text-2xl font-bold">
								<Translate text="collections.overview.features_title" />
							</h2>
							<div className="h-px flex-1 bg-gradient-to-r from-border to-transparent" />
							{/* Tutorial button for experienced users */}
							{wordCount > 0 && (
								<Button
									variant="outline"
									size="sm"
									onClick={handleStartTutorial}
									className="text-muted-foreground hover:text-foreground"
								>
									<Translate text="collections.tutorial.start" />
								</Button>
							)}
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
							{features.map((feature) => (
								<div key={feature.titleKey} className={cn(feature.className)}>
									<FeatureCard
										titleKey={feature.titleKey}
										descriptionKey={feature.descriptionKey}
										icon={feature.icon}
										link={feature.link}
										subFeatures={feature.subFeatures}
										status={feature.status}
										wordCount={feature.wordCount}
										requiredWords={feature.requiredWords}
										tutorialTarget={feature.tutorialTarget}
									/>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
